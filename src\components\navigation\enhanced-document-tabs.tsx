"use client";

import React, { useState, useCallback, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import {
  X,
  ChevronLeft,
  ChevronRight,
  Plus,
  FileText,
  AlertCircle,
  Loader2,
  MoreHorizontal,
  Pin,
  PinOff,
  Grid3X3,
  List,
  Move
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuLabel,
} from '@/components/ui/dropdown-menu';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';
import type { DocumentInstance } from '@/lib/types/pdf';

export type TabDisplayMode = 'full' | 'compact' | 'minimal' | 'icons-only';
export type TabLayout = 'horizontal' | 'vertical' | 'grid';

interface EnhancedDocumentTabsProps {
  documents: DocumentInstance[];
  activeDocumentId: string | null;
  onDocumentSelect: (documentId: string) => void;
  onDocumentClose: (documentId: string) => void;
  onNewDocument: () => void;
  onDocumentReorder?: (fromIndex: number, toIndex: number) => void;
  displayMode?: TabDisplayMode;
  layout?: TabLayout;
  maxVisibleTabs?: number;
  showPinnedTabs?: boolean;
  pinnedDocuments?: string[];
  onDocumentPin?: (documentId: string, pinned: boolean) => void;
  className?: string;
}

interface TabItemProps {
  document: DocumentInstance;
  isActive: boolean;
  isPinned?: boolean;
  displayMode: TabDisplayMode;
  onSelect: () => void;
  onClose: (e: React.MouseEvent) => void;
  onPin?: (e: React.MouseEvent) => void;
  isDragging?: boolean;
  onDragStart?: (e: React.DragEvent) => void;
  onDragEnd?: (e: React.DragEvent) => void;
}

const TabItem: React.FC<TabItemProps> = ({
  document,
  isActive,
  isPinned = false,
  displayMode,
  onSelect,
  onClose,
  onPin,
  isDragging = false,
  onDragStart,
  onDragEnd
}) => {
  const [isHovered, setIsHovered] = useState(false);

  const getTabIcon = () => {
    if (document.isLoading) {
      return <Loader2 className="h-3 w-3 animate-spin" />;
    }
    if (document.hasError) {
      return <AlertCircle className="h-3 w-3 text-destructive" />;
    }
    return <FileText className="h-3 w-3" />;
  };

  const getTabTitle = () => {
    if (displayMode === 'icons-only') return null;
    
    const maxLength = displayMode === 'minimal' ? 8 : displayMode === 'compact' ? 12 : 20;
    return document.title.length > maxLength 
      ? `${document.title.substring(0, maxLength)}...` 
      : document.title;
  };

  const getTabWidth = () => {
    switch (displayMode) {
      case 'icons-only':
        return 'w-8';
      case 'minimal':
        return 'w-16';
      case 'compact':
        return 'w-24';
      case 'full':
        return 'w-32';
      default:
        return 'w-32';
    }
  };

  return (
    <div
      className={cn(
        "relative flex items-center gap-1 px-2 py-1 text-sm border-b-2 cursor-pointer transition-all duration-200 min-w-0 shrink-0",
        getTabWidth(),
        isActive 
          ? "border-primary bg-background text-foreground" 
          : "border-transparent bg-muted/50 text-muted-foreground hover:bg-muted hover:text-foreground",
        document.hasError && "border-destructive/50",
        isDragging && "opacity-50 scale-95",
        isPinned && "bg-primary/5"
      )}
      onClick={onSelect}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      draggable={onDragStart ? true : false}
      onDragStart={onDragStart}
      onDragEnd={onDragEnd}
      title={`${document.title}${document.hasError ? ' (Error)' : ''}${document.isLoading ? ' (Loading...)' : ''}${isPinned ? ' (Pinned)' : ''}`}
    >
      {/* Pin indicator */}
      {isPinned && (
        <div className="absolute -top-1 -left-1 w-2 h-2 bg-primary rounded-full" />
      )}

      {getTabIcon()}
      
      {displayMode !== 'icons-only' && (
        <span className="truncate flex-1 min-w-0 text-xs">
          {getTabTitle()}
        </span>
      )}
      
      {/* Action buttons */}
      {(isHovered || isActive) && displayMode !== 'icons-only' && (
        <div className="flex items-center gap-0.5">
          {/* Pin button */}
          {onPin && (
            <Button
              variant="ghost"
              size="sm"
              className="h-4 w-4 p-0 hover:bg-primary/20"
              onClick={onPin}
              title={isPinned ? "Unpin document" : "Pin document"}
            >
              {isPinned ? (
                <PinOff className="h-2.5 w-2.5" />
              ) : (
                <Pin className="h-2.5 w-2.5" />
              )}
            </Button>
          )}
          
          {/* Close button */}
          <Button
            variant="ghost"
            size="sm"
            className="h-4 w-4 p-0 hover:bg-destructive hover:text-destructive-foreground"
            onClick={onClose}
            title="Close document"
          >
            <X className="h-2.5 w-2.5" />
          </Button>
        </div>
      )}
    </div>
  );
};

export default function EnhancedDocumentTabs({
  documents,
  activeDocumentId,
  onDocumentSelect,
  onDocumentClose,
  onNewDocument,
  onDocumentReorder,
  displayMode = 'compact',
  layout = 'horizontal',
  maxVisibleTabs = 8,
  showPinnedTabs = true,
  pinnedDocuments = [],
  onDocumentPin,
  className
}: EnhancedDocumentTabsProps) {
  const [scrollPosition, setScrollPosition] = useState(0);
  const [showScrollButtons, setShowScrollButtons] = useState(false);
  const [draggedIndex, setDraggedIndex] = useState<number | null>(null);
  const [dropTargetIndex, setDropTargetIndex] = useState<number | null>(null);
  
  const tabsContainerRef = useRef<HTMLDivElement>(null);
  const tabsScrollRef = useRef<HTMLDivElement>(null);

  // Separate pinned and unpinned documents
  const pinnedDocs = showPinnedTabs 
    ? documents.filter(doc => pinnedDocuments.includes(doc.id))
    : [];
  const unpinnedDocs = documents.filter(doc => !pinnedDocuments.includes(doc.id));
  const allDocs = [...pinnedDocs, ...unpinnedDocs];

  // Check if scrolling is needed
  useEffect(() => {
    const checkScrollNeeded = () => {
      if (tabsContainerRef.current && tabsScrollRef.current) {
        const containerWidth = tabsContainerRef.current.offsetWidth;
        const scrollWidth = tabsScrollRef.current.scrollWidth;
        setShowScrollButtons(scrollWidth > containerWidth);
      }
    };

    checkScrollNeeded();
    window.addEventListener('resize', checkScrollNeeded);
    return () => window.removeEventListener('resize', checkScrollNeeded);
  }, [documents, displayMode]);

  // Handle tab scrolling
  const scrollTabs = useCallback((direction: 'left' | 'right') => {
    if (!tabsScrollRef.current) return;
    
    const scrollAmount = 200;
    const newPosition = direction === 'left' 
      ? Math.max(0, scrollPosition - scrollAmount)
      : scrollPosition + scrollAmount;
    
    tabsScrollRef.current.scrollTo({ left: newPosition, behavior: 'smooth' });
    setScrollPosition(newPosition);
  }, [scrollPosition]);

  // Handle tab close
  const handleTabClose = useCallback((e: React.MouseEvent, documentId: string) => {
    e.stopPropagation();
    onDocumentClose(documentId);
  }, [onDocumentClose]);

  // Handle tab pin
  const handleTabPin = useCallback((e: React.MouseEvent, documentId: string) => {
    e.stopPropagation();
    if (onDocumentPin) {
      const isPinned = pinnedDocuments.includes(documentId);
      onDocumentPin(documentId, !isPinned);
    }
  }, [onDocumentPin, pinnedDocuments]);

  // Handle drag and drop
  const handleDragStart = useCallback((e: React.DragEvent, index: number) => {
    setDraggedIndex(index);
    e.dataTransfer.effectAllowed = 'move';
  }, []);

  const handleDragEnd = useCallback(() => {
    if (draggedIndex !== null && dropTargetIndex !== null && onDocumentReorder) {
      onDocumentReorder(draggedIndex, dropTargetIndex);
    }
    setDraggedIndex(null);
    setDropTargetIndex(null);
  }, [draggedIndex, dropTargetIndex, onDocumentReorder]);

  const handleDragOver = useCallback((e: React.DragEvent, index: number) => {
    e.preventDefault();
    setDropTargetIndex(index);
  }, []);

  // Get visible documents based on max visible tabs
  const getVisibleDocuments = () => {
    if (allDocs.length <= maxVisibleTabs) {
      return allDocs;
    }

    // Always show active document
    const activeIndex = allDocs.findIndex(doc => doc.id === activeDocumentId);
    if (activeIndex === -1) {
      return allDocs.slice(0, maxVisibleTabs);
    }

    // Show documents around active document
    const halfVisible = Math.floor(maxVisibleTabs / 2);
    const start = Math.max(0, activeIndex - halfVisible);
    const end = Math.min(allDocs.length, start + maxVisibleTabs);
    
    return allDocs.slice(start, end);
  };

  const visibleDocuments = getVisibleDocuments();
  const hiddenCount = allDocs.length - visibleDocuments.length;

  // Render overflow menu for hidden tabs
  const renderOverflowMenu = () => {
    if (hiddenCount === 0) return null;

    const hiddenDocs = allDocs.filter(doc => !visibleDocuments.includes(doc));

    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            className="h-8 px-2 shrink-0"
          >
            <MoreHorizontal className="h-4 w-4" />
            <span className="ml-1 text-xs">+{hiddenCount}</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-56">
          <DropdownMenuLabel>Hidden Documents</DropdownMenuLabel>
          <DropdownMenuSeparator />
          {hiddenDocs.map((document) => (
            <DropdownMenuItem
              key={document.id}
              onClick={() => onDocumentSelect(document.id)}
              className="flex items-center gap-2"
            >
              <FileText className="h-4 w-4" />
              <span className="truncate">{document.title}</span>
              {pinnedDocuments.includes(document.id) && (
                <Pin className="h-3 w-3 text-primary" />
              )}
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    );
  };

  // Render display mode selector
  const renderDisplayModeSelector = () => (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
          <Grid3X3 className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>Display Mode</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={() => {}}>
          <List className="h-4 w-4 mr-2" />
          Full
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => {}}>
          <Grid3X3 className="h-4 w-4 mr-2" />
          Compact
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => {}}>
          <MoreHorizontal className="h-4 w-4 mr-2" />
          Minimal
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );

  if (layout === 'horizontal') {
    return (
      <TooltipProvider>
        <div className={cn("flex items-center border-b bg-background", className)}>
          {/* Scroll left button */}
          {showScrollButtons && (
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 shrink-0"
              onClick={() => scrollTabs('left')}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
          )}

          {/* Tabs container */}
          <div 
            ref={tabsContainerRef}
            className="flex-1 overflow-hidden"
          >
            <div
              ref={tabsScrollRef}
              className="flex overflow-x-auto scrollbar-hide"
              style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
            >
              {visibleDocuments.map((document, index) => (
                <TabItem
                  key={document.id}
                  document={document}
                  isActive={document.id === activeDocumentId}
                  isPinned={pinnedDocuments.includes(document.id)}
                  displayMode={displayMode}
                  onSelect={() => onDocumentSelect(document.id)}
                  onClose={(e) => handleTabClose(e, document.id)}
                  onPin={onDocumentPin ? (e) => handleTabPin(e, document.id) : undefined}
                  isDragging={draggedIndex === index}
                  onDragStart={onDocumentReorder ? (e) => handleDragStart(e, index) : undefined}
                  onDragEnd={onDocumentReorder ? handleDragEnd : undefined}
                />
              ))}
            </div>
          </div>

          {/* Scroll right button */}
          {showScrollButtons && (
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 shrink-0"
              onClick={() => scrollTabs('right')}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          )}

          {/* Overflow menu */}
          {renderOverflowMenu()}

          {/* Controls */}
          <div className="flex items-center gap-1 px-2">
            <Separator orientation="vertical" className="h-6" />
            
            {/* New document button */}
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onNewDocument}
                  className="h-8 w-8 p-0"
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Open new document</TooltipContent>
            </Tooltip>

            {/* Display mode selector */}
            {renderDisplayModeSelector()}
          </div>
        </div>
      </TooltipProvider>
    );
  }

  // Vertical layout (for sidebar)
  return (
    <TooltipProvider>
      <div className={cn("flex flex-col", className)}>
        {/* Header */}
        <div className="flex items-center justify-between p-2 border-b">
          <span className="text-sm font-medium">Documents</span>
          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={onNewDocument}
              className="h-6 w-6 p-0"
            >
              <Plus className="h-3 w-3" />
            </Button>
            {renderDisplayModeSelector()}
          </div>
        </div>

        {/* Document list */}
        <div className="flex-1 overflow-auto">
          {allDocs.map((document, index) => (
            <div
              key={document.id}
              className={cn(
                "flex items-center gap-2 p-2 hover:bg-muted cursor-pointer",
                document.id === activeDocumentId && "bg-primary/10"
              )}
              onClick={() => onDocumentSelect(document.id)}
            >
              <FileText className="h-4 w-4 shrink-0" />
              <span className="truncate flex-1 text-sm">{document.title}</span>
              {pinnedDocuments.includes(document.id) && (
                <Pin className="h-3 w-3 text-primary shrink-0" />
              )}
            </div>
          ))}
        </div>
      </div>
    </TooltipProvider>
  );
}
