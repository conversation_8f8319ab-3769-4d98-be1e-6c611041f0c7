"use client";

import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { 
  X, 
  FileText, 
  Plus, 
  ChevronLeft, 
  ChevronRight,
  MoreHorizontal,
  AlertCircle,
  Loader2
} from 'lucide-react';
import { cn } from '@/lib/utils';
import type { DocumentInstance } from '@/lib/types/pdf';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { toast } from 'sonner';

interface DocumentTabsProps {
  documents: DocumentInstance[];
  activeDocumentId: string | null;
  onDocumentSelect: (documentId: string) => void;
  onDocumentClose: (documentId: string) => void;
  onNewDocument: () => void;
  maxVisibleTabs?: number;
  className?: string;
}

interface TabItemProps {
  document: DocumentInstance;
  isActive: boolean;
  onSelect: () => void;
  onClose: (e: React.MouseEvent) => void;
  isCompact?: boolean;
}

const TabItem: React.FC<TabItemProps> = ({
  document,
  isActive,
  onSelect,
  onClose,
  isCompact = false
}) => {
  const [isHovered, setIsHovered] = useState(false);

  const getTabIcon = () => {
    if (document.isLoading) {
      return <Loader2 className="h-3 w-3 animate-spin" />;
    }
    if (document.hasError) {
      return <AlertCircle className="h-3 w-3 text-destructive" />;
    }
    return <FileText className="h-3 w-3" />;
  };

  const getTabTitle = () => {
    const maxLength = isCompact ? 15 : 25;
    return document.title.length > maxLength 
      ? `${document.title.substring(0, maxLength)}...` 
      : document.title;
  };

  return (
    <div
      className={cn(
        "relative flex items-center gap-2 px-3 py-2 text-sm border-b-2 cursor-pointer transition-all duration-200 min-w-0",
        isActive 
          ? "border-primary bg-background text-foreground" 
          : "border-transparent bg-muted/50 text-muted-foreground hover:bg-muted hover:text-foreground",
        document.hasError && "border-destructive/50",
        isCompact && "px-2 py-1"
      )}
      onClick={onSelect}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      title={`${document.title}${document.hasError ? ' (Error)' : ''}${document.isLoading ? ' (Loading...)' : ''}`}
    >
      {getTabIcon()}
      <span className="truncate flex-1 min-w-0">
        {getTabTitle()}
      </span>
      
      {(isHovered || isActive) && (
        <Button
          variant="ghost"
          size="sm"
          className="h-4 w-4 p-0 hover:bg-destructive hover:text-destructive-foreground"
          onClick={onClose}
          title="Close document"
        >
          <X className="h-3 w-3" />
        </Button>
      )}
    </div>
  );
};

export default function DocumentTabs({
  documents,
  activeDocumentId,
  onDocumentSelect,
  onDocumentClose,
  onNewDocument,
  maxVisibleTabs = 6,
  className
}: DocumentTabsProps) {
  const [scrollPosition, setScrollPosition] = useState(0);
  const [showScrollButtons, setShowScrollButtons] = useState(false);
  const tabsContainerRef = useRef<HTMLDivElement>(null);
  const tabsScrollRef = useRef<HTMLDivElement>(null);

  // Check if scrolling is needed
  useEffect(() => {
    const checkScrollNeeded = () => {
      if (tabsContainerRef.current && tabsScrollRef.current) {
        const containerWidth = tabsContainerRef.current.clientWidth;
        const scrollWidth = tabsScrollRef.current.scrollWidth;
        setShowScrollButtons(scrollWidth > containerWidth);
      }
    };

    checkScrollNeeded();
    window.addEventListener('resize', checkScrollNeeded);
    return () => window.removeEventListener('resize', checkScrollNeeded);
  }, [documents]);

  const scrollTabs = (direction: 'left' | 'right') => {
    if (!tabsScrollRef.current) return;
    
    const scrollAmount = 200;
    const newPosition = direction === 'left' 
      ? Math.max(0, scrollPosition - scrollAmount)
      : scrollPosition + scrollAmount;
    
    tabsScrollRef.current.scrollTo({ left: newPosition, behavior: 'smooth' });
    setScrollPosition(newPosition);
  };

  const handleTabClose = (e: React.MouseEvent, documentId: string) => {
    e.stopPropagation();
    onDocumentClose(documentId);
  };

  const visibleDocuments = documents.slice(0, maxVisibleTabs);
  const hiddenDocuments = documents.slice(maxVisibleTabs);

  if (documents.length === 0) {
    return (
      <Card className={cn("border-b rounded-none", className)}>
        <div className="flex items-center justify-between p-2">
          <div className="flex items-center gap-2 text-muted-foreground">
            <FileText className="h-4 w-4" />
            <span className="text-sm">No documents open</span>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onNewDocument}
            className="h-8 px-3"
          >
            <Plus className="h-4 w-4 mr-1" />
            Open Document
          </Button>
        </div>
      </Card>
    );
  }

  return (
    <Card className={cn("border-b rounded-none", className)}>
      <div className="flex items-center">
        {/* Scroll left button */}
        {showScrollButtons && (
          <Button
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0 shrink-0"
            onClick={() => scrollTabs('left')}
            disabled={scrollPosition === 0}
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
        )}

        {/* Tabs container */}
        <div 
          ref={tabsContainerRef}
          className="flex-1 overflow-hidden"
        >
          <div
            ref={tabsScrollRef}
            className="flex overflow-x-auto scrollbar-hide"
            style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
          >
            {visibleDocuments.map((document) => (
              <TabItem
                key={document.id}
                document={document}
                isActive={document.id === activeDocumentId}
                onSelect={() => onDocumentSelect(document.id)}
                onClose={(e) => handleTabClose(e, document.id)}
                isCompact={documents.length > 4}
              />
            ))}
          </div>
        </div>

        {/* Scroll right button */}
        {showScrollButtons && (
          <Button
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0 shrink-0"
            onClick={() => scrollTabs('right')}
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        )}

        {/* Overflow menu for hidden tabs */}
        {hiddenDocuments.length > 0 && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 shrink-0"
              >
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              {hiddenDocuments.map((document) => (
                <DropdownMenuItem
                  key={document.id}
                  onClick={() => onDocumentSelect(document.id)}
                  className="flex items-center gap-2"
                >
                  {document.isLoading ? (
                    <Loader2 className="h-3 w-3 animate-spin" />
                  ) : document.hasError ? (
                    <AlertCircle className="h-3 w-3 text-destructive" />
                  ) : (
                    <FileText className="h-3 w-3" />
                  )}
                  <span className="truncate flex-1">{document.title}</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-4 w-4 p-0 hover:bg-destructive hover:text-destructive-foreground"
                    onClick={(e) => handleTabClose(e, document.id)}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        )}

        <Separator orientation="vertical" className="h-6" />

        {/* New document button */}
        <Button
          variant="ghost"
          size="sm"
          onClick={onNewDocument}
          className="h-8 px-3 shrink-0"
          title="Open new document"
        >
          <Plus className="h-4 w-4" />
        </Button>
      </div>
    </Card>
  );
}
