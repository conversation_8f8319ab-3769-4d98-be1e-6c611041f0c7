import type { PDFDocumentProxy, PDFPageProxy } from 'pdfjs-dist';

// Re-export the proper PDF types from pdfjs-dist
export type { PDFDocumentProxy, PDFPageProxy } from 'pdfjs-dist';

// Type for the document callback from react-pdf
export type DocumentCallback = PDFDocumentProxy;

// Type for page callback from react-pdf
export type PageCallback = PDFPageProxy & {
  width: number;
  height: number;
  originalWidth: number;
  originalHeight: number;
};

// Union type for PDF document that can be either the direct proxy or wrapped
export type PDFDocument = PDFDocumentProxy | {
  _pdfInfo?: {
    pdfDocument: PDFDocumentProxy;
  };
} | unknown;

// Type guard to check if an object is a PDFDocumentProxy
export function isPDFDocumentProxy(obj: unknown): obj is PDFDocumentProxy {
  return (
    obj !== null &&
    typeof obj === 'object' &&
    'numPages' in obj &&
    'getPage' in obj &&
    typeof (obj as Record<string, unknown>).getPage === 'function'
  );
}

// Helper function to extract the actual PDF document from various formats
export function extractPDFDocument(pdfDocument: PDFDocument): PDFDocumentProxy | null {
  if (!pdfDocument) return null;
  
  // Check if it's already a PDFDocumentProxy
  if (isPDFDocumentProxy(pdfDocument)) {
    return pdfDocument;
  }
  
  // Check if it has the _pdfInfo wrapper (legacy format)
  if (
    typeof pdfDocument === 'object' &&
    pdfDocument !== null &&
    '_pdfInfo' in pdfDocument &&
    pdfDocument._pdfInfo &&
    typeof pdfDocument._pdfInfo === 'object' &&
    'pdfDocument' in pdfDocument._pdfInfo
  ) {
    const wrapped = pdfDocument._pdfInfo.pdfDocument;
    if (isPDFDocumentProxy(wrapped)) {
      return wrapped;
    }
  }
  
  return null;
}

// Type for outline items
export interface OutlineItem {
  title: string;
  bold?: boolean;
  italic?: boolean;
  color?: [number, number, number] | null;
  dest?: unknown;
  url?: string;
  items?: OutlineItem[];
}

// Type for text content items
export interface TextContentItem {
  str: string;
  dir: string;
  width: number;
  height: number;
  transform: number[];
  fontName: string;
  hasEOL: boolean;
}

// Type for text content
export interface TextContent {
  items: TextContentItem[];
  styles: Record<string, unknown>;
}

// Document instance interface for multi-document support
export interface DocumentInstance {
  id: string;
  file: string | File;
  title: string;
  isLoading: boolean;
  hasError: boolean;
  errorMessage?: string;
  numPages: number;
  pdfDocument?: PDFDocumentProxy;
  outline?: OutlineItem[];
  lastAccessed: number;
  // Document-specific state
  pageNumber: number;
  scale: number;
  rotation: number;
  searchText: string;
  bookmarks: Array<{ id: string; page: number; title: string; timestamp: number }>;
  annotations: unknown[];
  formData: Record<string, unknown>;
}

// Document manager state
export interface DocumentManagerState {
  documents: Map<string, DocumentInstance>;
  activeDocumentId: string | null;
  maxDocuments: number;
  memoryThreshold: number;
}

// Error types for better error handling
export enum DocumentErrorType {
  LOAD_FAILED = 'LOAD_FAILED',
  CORRUPTED_FILE = 'CORRUPTED_FILE',
  UNSUPPORTED_FORMAT = 'UNSUPPORTED_FORMAT',
  FILE_NOT_FOUND = 'FILE_NOT_FOUND',
  NETWORK_ERROR = 'NETWORK_ERROR',
  MEMORY_ERROR = 'MEMORY_ERROR',
  PERMISSION_DENIED = 'PERMISSION_DENIED'
}

export interface DocumentError {
  type: DocumentErrorType;
  message: string;
  originalError?: Error;
  canRetry: boolean;
  suggestedAction?: string;
}

// Helper function to generate document title from file
export function generateDocumentTitle(file: string | File): string {
  if (typeof file === 'string') {
    // Extract filename from URL
    try {
      const url = new URL(file);
      const pathname = url.pathname;
      const filename = pathname.split('/').pop() || 'Document';
      return filename.replace(/\.[^/.]+$/, ''); // Remove extension
    } catch {
      return 'Remote Document';
    }
  } else {
    // Extract filename from File object
    return file.name.replace(/\.[^/.]+$/, ''); // Remove extension
  }
}

// Helper function to categorize errors
export function categorizeError(error: Error): DocumentError {
  const message = error.message.toLowerCase();

  if (message.includes('network') || message.includes('fetch')) {
    return {
      type: DocumentErrorType.NETWORK_ERROR,
      message: 'Network error occurred while loading the document',
      originalError: error,
      canRetry: true,
      suggestedAction: 'Check your internet connection and try again'
    };
  }

  if (message.includes('corrupted') || message.includes('invalid pdf')) {
    return {
      type: DocumentErrorType.CORRUPTED_FILE,
      message: 'The PDF file appears to be corrupted or invalid',
      originalError: error,
      canRetry: false,
      suggestedAction: 'Try opening a different PDF file'
    };
  }

  if (message.includes('not found') || message.includes('404')) {
    return {
      type: DocumentErrorType.FILE_NOT_FOUND,
      message: 'The PDF file could not be found',
      originalError: error,
      canRetry: false,
      suggestedAction: 'Verify the file path or URL is correct'
    };
  }

  if (message.includes('permission') || message.includes('unauthorized')) {
    return {
      type: DocumentErrorType.PERMISSION_DENIED,
      message: 'Permission denied accessing the PDF file',
      originalError: error,
      canRetry: false,
      suggestedAction: 'Check file permissions or authentication'
    };
  }

  if (message.includes('memory') || message.includes('out of memory')) {
    return {
      type: DocumentErrorType.MEMORY_ERROR,
      message: 'Insufficient memory to load the document',
      originalError: error,
      canRetry: true,
      suggestedAction: 'Close other documents and try again'
    };
  }

  // Default case
  return {
    type: DocumentErrorType.LOAD_FAILED,
    message: error.message || 'Failed to load PDF document',
    originalError: error,
    canRetry: true,
    suggestedAction: 'Try reloading the document'
  };
}
