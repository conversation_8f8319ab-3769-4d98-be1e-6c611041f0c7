"use client";

import React, { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { 
  AlertCircle, 
  RefreshCw, 
  FolderOpen, 
  FileText, 
  Download,
  ExternalLink,
  Info,
  AlertTriangle,
  XCircle
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';
import type { DocumentError, DocumentErrorType } from '@/lib/types/pdf';

interface ErrorRecoveryProps {
  error: DocumentError;
  documentTitle: string;
  onRetry: () => void;
  onSelectNewFile: (file: File) => void;
  onSelectNewUrl: (url: string) => void;
  onCancel: () => void;
  className?: string;
}

interface FileDialogProps {
  onFileSelect: (file: File) => void;
  onCancel: () => void;
}

const FileDialog: React.FC<FileDialogProps> = ({ onFileSelect, onCancel }) => {
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && file.type === 'application/pdf') {
      onFileSelect(file);
    } else {
      toast.error('Invalid file type', {
        description: 'Please select a PDF file.',
      });
    }
  };

  const handleBrowseClick = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className="space-y-4">
      <div className="text-center">
        <FolderOpen className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
        <h3 className="text-lg font-semibold mb-2">Select a Different PDF File</h3>
        <p className="text-sm text-muted-foreground mb-4">
          Choose a PDF file from your device to replace the failed document.
        </p>
      </div>
      
      <div className="flex gap-2">
        <Button onClick={handleBrowseClick} className="flex-1">
          <FolderOpen className="h-4 w-4 mr-2" />
          Browse Files
        </Button>
        <Button variant="outline" onClick={onCancel}>
          Cancel
        </Button>
      </div>
      
      <input
        ref={fileInputRef}
        type="file"
        accept=".pdf"
        onChange={handleFileChange}
        className="hidden"
      />
    </div>
  );
};

interface UrlDialogProps {
  onUrlSelect: (url: string) => void;
  onCancel: () => void;
}

const UrlDialog: React.FC<UrlDialogProps> = ({ onUrlSelect, onCancel }) => {
  const [url, setUrl] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async () => {
    if (!url.trim()) return;

    setIsLoading(true);
    try {
      // Validate URL format
      new URL(url);
      onUrlSelect(url);
    } catch {
      toast.error('Invalid URL', {
        description: 'Please enter a valid URL.',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-4">
      <div className="text-center">
        <ExternalLink className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
        <h3 className="text-lg font-semibold mb-2">Enter PDF URL</h3>
        <p className="text-sm text-muted-foreground mb-4">
          Provide a direct link to a PDF file to replace the failed document.
        </p>
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="pdf-url">PDF URL</Label>
        <Input
          id="pdf-url"
          type="url"
          placeholder="https://example.com/document.pdf"
          value={url}
          onChange={(e) => setUrl(e.target.value)}
          onKeyDown={(e) => e.key === 'Enter' && handleSubmit()}
        />
      </div>
      
      <div className="flex gap-2">
        <Button 
          onClick={handleSubmit} 
          disabled={!url.trim() || isLoading}
          className="flex-1"
        >
          {isLoading ? 'Loading...' : 'Load PDF'}
        </Button>
        <Button variant="outline" onClick={onCancel}>
          Cancel
        </Button>
      </div>
    </div>
  );
};

const getErrorIcon = (errorType: DocumentErrorType) => {
  switch (errorType) {
    case 'NETWORK_ERROR':
      return <Download className="h-6 w-6 text-orange-500" />;
    case 'FILE_NOT_FOUND':
      return <FileText className="h-6 w-6 text-red-500" />;
    case 'CORRUPTED_FILE':
      return <XCircle className="h-6 w-6 text-red-500" />;
    case 'PERMISSION_DENIED':
      return <AlertTriangle className="h-6 w-6 text-yellow-500" />;
    case 'MEMORY_ERROR':
      return <AlertCircle className="h-6 w-6 text-purple-500" />;
    default:
      return <AlertCircle className="h-6 w-6 text-red-500" />;
  }
};

const getErrorColor = (errorType: DocumentErrorType) => {
  switch (errorType) {
    case 'NETWORK_ERROR':
      return 'border-orange-200 bg-orange-50';
    case 'FILE_NOT_FOUND':
      return 'border-red-200 bg-red-50';
    case 'CORRUPTED_FILE':
      return 'border-red-200 bg-red-50';
    case 'PERMISSION_DENIED':
      return 'border-yellow-200 bg-yellow-50';
    case 'MEMORY_ERROR':
      return 'border-purple-200 bg-purple-50';
    default:
      return 'border-red-200 bg-red-50';
  }
};

export default function ErrorRecovery({
  error,
  documentTitle,
  onRetry,
  onSelectNewFile,
  onSelectNewUrl,
  onCancel,
  className
}: ErrorRecoveryProps) {
  const [showFileDialog, setShowFileDialog] = useState(false);
  const [showUrlDialog, setShowUrlDialog] = useState(false);

  const handleFileSelect = (file: File) => {
    setShowFileDialog(false);
    onSelectNewFile(file);
  };

  const handleUrlSelect = (url: string) => {
    setShowUrlDialog(false);
    onSelectNewUrl(url);
  };

  if (showFileDialog) {
    return (
      <Card className={cn("max-w-md mx-auto", className)}>
        <CardContent className="p-6">
          <FileDialog
            onFileSelect={handleFileSelect}
            onCancel={() => setShowFileDialog(false)}
          />
        </CardContent>
      </Card>
    );
  }

  if (showUrlDialog) {
    return (
      <Card className={cn("max-w-md mx-auto", className)}>
        <CardContent className="p-6">
          <UrlDialog
            onUrlSelect={handleUrlSelect}
            onCancel={() => setShowUrlDialog(false)}
          />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn("max-w-md mx-auto", getErrorColor(error.type), className)}>
      <CardHeader className="text-center">
        <div className="flex justify-center mb-4">
          {getErrorIcon(error.type)}
        </div>
        <CardTitle className="text-lg">Failed to Load Document</CardTitle>
        <CardDescription className="text-base">
          <strong>{documentTitle}</strong>
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-4">
        <div className="bg-background/50 rounded-lg p-3 border">
          <div className="flex items-start gap-2">
            <Info className="h-4 w-4 text-muted-foreground mt-0.5 shrink-0" />
            <div className="space-y-1">
              <p className="text-sm font-medium">{error.message}</p>
              {error.suggestedAction && (
                <p className="text-xs text-muted-foreground">{error.suggestedAction}</p>
              )}
            </div>
          </div>
        </div>

        <div className="space-y-2">
          {error.canRetry && (
            <Button onClick={onRetry} className="w-full">
              <RefreshCw className="h-4 w-4 mr-2" />
              Try Again
            </Button>
          )}
          
          <div className="grid grid-cols-2 gap-2">
            <Button 
              variant="outline" 
              onClick={() => setShowFileDialog(true)}
              className="text-xs"
            >
              <FolderOpen className="h-3 w-3 mr-1" />
              Browse File
            </Button>
            <Button 
              variant="outline" 
              onClick={() => setShowUrlDialog(true)}
              className="text-xs"
            >
              <ExternalLink className="h-3 w-3 mr-1" />
              Enter URL
            </Button>
          </div>
        </div>

        <Separator />
        
        <Button variant="ghost" onClick={onCancel} className="w-full">
          Close Document
        </Button>
      </CardContent>
    </Card>
  );
}
