"use client";

import type React from "react";

import { useState, use<PERSON>allback, useRef, useEffect } from "react";
import { Document, pdfjs } from "react-pdf";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import {
  ChevronLeft,
  ChevronRight,
  ZoomIn,
  ZoomOut,
  Download,
  X,
  RotateCcw,
  Maximize,
  Minimize,
  Menu,
  Bookmark,
  BookmarkPlus,
  Sidebar,
  PenTool,
  Grid3X3,
  Printer,
  FileText,
  Edit,
  GitBranch,
} from "lucide-react";
import { toast } from "sonner";
import PDFSimplePage from "./pdf-simple-page";
import PDFSidebar from "../navigation/pdf-sidebar";
import type {
  Annotation,
  AnnotationType,
} from "../annotations/pdf-annotations";
import { extractPDFDocument } from "@/lib/types/pdf";
import type { OutlineItem } from "@/lib/types/pdf";
import type { FormField, FormData, FormValidationResult } from "../forms/pdf-form-manager";
import PDFFloatingToolbarManager from "../navigation/pdf-floating-toolbar-manager";
import { useAnnotationHistory } from "../annotations/pdf-annotation-history";


// Set up PDF.js worker
pdfjs.GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.js`;

interface PDFViewerProps {
  file: string | File;
  onClose: () => void;
}

export default function PDFViewer({ file, onClose }: PDFViewerProps) {
  const [numPages, setNumPages] = useState<number>(0);
  const [pageNumber, setPageNumber] = useState<number>(1);
  const [scale, setScale] = useState<number>(1.0);
  const [rotation, setRotation] = useState<number>(0);
  const [searchText, setSearchText] = useState<string>("");
  const [outline, setOutline] = useState<OutlineItem[]>([]);
  const containerRef = useRef<HTMLDivElement>(null);
  const [bookmarks, setBookmarks] = useState<
    Array<{ id: string; page: number; title: string; timestamp: number }>
  >([]);
  const [pdfDocument, setPdfDocument] = useState<unknown>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isFullscreen, setIsFullscreen] = useState<boolean>(false);

  // Sidebar state
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [activeTab, setActiveTab] = useState<
    | "outline"
    | "search"
    | "bookmarks"
    | "images"
    | "annotations"
    | "export"
    | "print"
    | "thumbnails"
    | "performance"
    | "forms"
    | "form-designer"
    | "form-validation"
    | "workflows"
    | "ocr"
    | "signatures"
    | "collaboration"
    | "accessibility"
    | "version-control"
    | "version-timeline"
    | "settings"
  >("outline");

  // Annotation state
  const [annotations, setAnnotations] = useState<Annotation[]>([]);
  const [selectedTool, setSelectedTool] = useState<AnnotationType | null>(null);
  const [selectedColor, setSelectedColor] = useState("#FFEB3B");

  // Form state
  const [formFields, setFormFields] = useState<FormField[]>([]);
  const [formData, setFormData] = useState<FormData>({});
  const [isFormDesignMode, setIsFormDesignMode] = useState(false);

  // Annotation history
  const {
    addToHistory,
    undo: undoAnnotation,
    redo: redoAnnotation,
    canUndo,
    canRedo,
  } = useAnnotationHistory();

  const onDocumentLoadSuccess = useCallback(async (pdf: { numPages: number; _pdfInfo?: { pdfDocument: unknown }; getOutline?: () => Promise<unknown[]> }) => {
    setIsLoading(true);
    setNumPages(pdf.numPages);
    setPdfDocument(pdf._pdfInfo?.pdfDocument || pdf);

    // Extract outline
    try {
      const pdfDoc = extractPDFDocument(pdf);
      if (pdfDoc) {
        const outline = await pdfDoc.getOutline();
        if (outline) {
          const processedOutline = await processOutline(outline);
          setOutline(processedOutline);
        }
      }
    } catch (error) {
      console.warn("Could not extract PDF outline:", error);
    }

    setIsLoading(false);
    toast.success("PDF loaded successfully", {
      description: `Document contains ${pdf.numPages} pages.`,
    });
  }, []);

  const processOutline = async (
    outline: unknown[]
  ): Promise<OutlineItem[]> => {
    const processItem = async (item: { title?: string; dest?: unknown; bold?: boolean; italic?: boolean; color?: unknown; items?: unknown[] }): Promise<OutlineItem> => {

      const processedItem: OutlineItem = {
        title: item.title || "Untitled",
        dest: item.dest,
        bold: item.bold,
        italic: item.italic,
        color: Array.isArray(item.color) && item.color.length === 3 ? item.color as [number, number, number] : null,
      };

      if (item.items && item.items.length > 0) {
        processedItem.items = await Promise.all(
          item.items.map((child: unknown) => processItem(child as { title?: string; dest?: unknown; bold?: boolean; italic?: boolean; color?: unknown; items?: unknown[] }))
        );
      }

      return processedItem;
    };

    return Promise.all(outline.map((item: unknown) => {
      // Type guard to ensure item has the expected structure
      if (typeof item !== 'object' || item === null) {
        return processItem({ title: "Untitled" });
      }
      return processItem(item as { title?: string; dest?: unknown; bold?: boolean; italic?: boolean; color?: unknown; items?: unknown[] });
    }));
  };

  const onDocumentLoadError = useCallback((error: Error) => {
    setIsLoading(false);
    toast.error("Failed to load PDF", {
      description: error.message,
    });
  }, []);

  // Navigation functions
  const goToPrevPage = useCallback(() => {
    setPageNumber((prev) => Math.max(1, prev - 1));
  }, []);

  const goToNextPage = useCallback(() => {
    setPageNumber((prev) => Math.min(numPages, prev + 1));
  }, [numPages]);

  const goToPage = useCallback(
    (page: number) => {
      if (page >= 1 && page <= numPages) {
        setPageNumber(page);
      }
    },
    [numPages]
  );

  // Zoom functions
  const zoomIn = useCallback(() => {
    setScale((prev) => Math.min(3.0, prev + 0.25));
  }, []);

  const zoomOut = useCallback(() => {
    setScale((prev) => Math.max(0.5, prev - 0.25));
  }, []);

  const resetZoom = useCallback(() => {
    setScale(1.0);
  }, []);

  const rotate = useCallback(() => {
    setRotation((prev) => (prev + 90) % 360);
  }, []);

  const toggleFullscreen = useCallback(() => {
    if (!document.fullscreenElement) {
      containerRef.current?.requestFullscreen();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  }, []);

  const downloadPDF = useCallback(() => {
    if (typeof file === "string") {
      const link = document.createElement("a");
      link.href = file;
      link.download = "document.pdf";
      link.click();
    } else {
      const url = URL.createObjectURL(file);
      const link = document.createElement("a");
      link.href = url;
      link.download = file.name;
      link.click();
      URL.revokeObjectURL(url);
    }
  }, [file]);

  const handlePageInputChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = Number.parseInt(e.target.value);
      if (!isNaN(value)) {
        goToPage(value);
      }
    },
    [goToPage]
  );

  // Bookmark functions
  const loadBookmarks = useCallback(() => {
    const savedBookmarks = localStorage.getItem("pdf-bookmarks");
    if (savedBookmarks) {
      setBookmarks(JSON.parse(savedBookmarks));
    }
  }, []);

  const saveBookmarks = useCallback((newBookmarks: typeof bookmarks) => {
    localStorage.setItem("pdf-bookmarks", JSON.stringify(newBookmarks));
    setBookmarks(newBookmarks);
  }, []);

  const addBookmark = useCallback(() => {
    const title = prompt("Enter bookmark title:", `Page ${pageNumber}`);
    if (title) {
      const newBookmark = {
        id: Date.now().toString(),
        page: pageNumber,
        title: title.trim(),
        timestamp: Date.now(),
      };
      const newBookmarks = [...bookmarks, newBookmark];
      saveBookmarks(newBookmarks);
      toast.success("Bookmark added", {
        description: `Page ${pageNumber} bookmarked as "${title}"`,
      });
    }
  }, [pageNumber, bookmarks, saveBookmarks]);

  const removeBookmark = useCallback(
    (bookmarkId: string) => {
      const newBookmarks = bookmarks.filter((b) => b.id !== bookmarkId);
      saveBookmarks(newBookmarks);
      toast.success("Bookmark removed", {
        description: "Bookmark has been deleted.",
      });
    },
    [bookmarks, saveBookmarks]
  );

  const updateBookmark = useCallback(
    (bookmarkId: string, newTitle: string) => {
      const newBookmarks = bookmarks.map((b) =>
        b.id === bookmarkId ? { ...b, title: newTitle } : b
      );
      saveBookmarks(newBookmarks);
    },
    [bookmarks, saveBookmarks]
  );

  const isPageBookmarked = useCallback(
    (page: number) => {
      return bookmarks.some((b) => b.page === page);
    },
    [bookmarks]
  );

  // Annotation functions
  const loadAnnotations = useCallback(() => {
    const savedAnnotations = localStorage.getItem("pdf-annotations");
    if (savedAnnotations) {
      setAnnotations(JSON.parse(savedAnnotations));
    }
  }, []);

  const saveAnnotations = useCallback((newAnnotations: Annotation[]) => {
    localStorage.setItem("pdf-annotations", JSON.stringify(newAnnotations));
    setAnnotations(newAnnotations);
  }, []);

  const addAnnotation = useCallback(
    (annotation: Omit<Annotation, "id" | "timestamp">) => {
      const newAnnotation: Annotation = {
        ...annotation,
        id: Date.now().toString(),
        timestamp: Date.now(),
      };
      const newAnnotations = [...annotations, newAnnotation];
      saveAnnotations(newAnnotations);

      // Add to history
      addToHistory({
        type: "add",
        annotation: newAnnotation,
      });

      toast.success("Annotation added", {
        description: `${annotation.type} annotation added to page ${annotation.pageNumber}`,
      });
    },
    [annotations, saveAnnotations, addToHistory]
  );

  const updateAnnotation = useCallback(
    (id: string, updates: Partial<Annotation>) => {
      const newAnnotations = annotations.map((ann) =>
        ann.id === id ? { ...ann, ...updates } : ann
      );
      saveAnnotations(newAnnotations);
    },
    [annotations, saveAnnotations]
  );

  const deleteAnnotation = useCallback(
    (id: string) => {
      const annotationToDelete = annotations.find((ann) => ann.id === id);
      if (!annotationToDelete) return;

      const newAnnotations = annotations.filter((ann) => ann.id !== id);
      saveAnnotations(newAnnotations);

      // Add to history
      addToHistory({
        type: "delete",
        annotation: annotationToDelete,
      });

      toast.success("Annotation deleted", {
        description: "Annotation has been removed.",
      });
    },
    [annotations, saveAnnotations, addToHistory]
  );

  // Form functions
  const loadFormData = useCallback(() => {
    const savedFormData = localStorage.getItem("pdf-form-data");
    if (savedFormData) {
      const parsed = JSON.parse(savedFormData);
      setFormData(parsed.formData || {});
    }
  }, []);

  const saveFormData = useCallback(
    (newFormData: FormData) => {
      const formDataWithMetadata = {
        formData: newFormData,
        metadata: {
          savedAt: new Date().toISOString(),
          version: "1.0",
          fieldCount: formFields.length,
        },
      };
      localStorage.setItem(
        "pdf-form-data",
        JSON.stringify(formDataWithMetadata)
      );
      setFormData(newFormData);
    },
    [formFields.length]
  );

  const openSidebar = useCallback((tab?: typeof activeTab) => {
    if (tab) setActiveTab(tab);
    setSidebarOpen(true);
  }, []);

  // Load data on component mount
  useEffect(() => {
    loadBookmarks();
    loadAnnotations();
    loadFormData();
  }, [loadBookmarks, loadAnnotations, loadFormData]);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.ctrlKey || e.metaKey) {
        switch (e.key) {
          case "f":
            e.preventDefault();
            openSidebar("search");
            break;
          case "b":
            e.preventDefault();
            openSidebar("bookmarks");
            break;
          case "d":
            e.preventDefault();
            addBookmark();
            break;
          case "a":
            e.preventDefault();
            openSidebar("annotations");
            break;
          case "p":
            e.preventDefault();
            openSidebar("print");
            break;
          case "t":
            e.preventDefault();
            openSidebar("thumbnails");
            break;
          case "r":
            e.preventDefault();
            openSidebar("forms");
            break;
          case "w":
            e.preventDefault();
            openSidebar("workflows");
            break;
          case "1":
            if (e.ctrlKey || e.metaKey) {
              e.preventDefault();
              openSidebar("outline");
            }
            break;
          case "2":
            if (e.ctrlKey || e.metaKey) {
              e.preventDefault();
              openSidebar("search");
            }
            break;
          case "3":
            if (e.ctrlKey || e.metaKey) {
              e.preventDefault();
              openSidebar("annotations");
            }
            break;
          case "4":
            if (e.ctrlKey || e.metaKey) {
              e.preventDefault();
              openSidebar("forms");
            }
            break;
        }
      }

      switch (e.key) {
        case "ArrowLeft":
          if (!e.ctrlKey && !e.metaKey) {
            e.preventDefault();
            goToPrevPage();
          }
          break;
        case "ArrowRight":
          if (!e.ctrlKey && !e.metaKey) {
            e.preventDefault();
            goToNextPage();
          }
          break;
        case "Escape":
          setSidebarOpen(false);
          setSelectedTool(null);
          // Clear any text selection
          window.getSelection()?.removeAllRanges();
          break;
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [goToPrevPage, goToNextPage, openSidebar, addBookmark]);

  const handleUndo = useCallback(() => {
    const action = undoAnnotation();
    if (!action) return;

    switch (action.type) {
      case "add":
        // Remove the annotation that was added
        const newAnnotations = annotations.filter(
          (ann) => ann.id !== action.annotation.id
        );
        saveAnnotations(newAnnotations);
        break;
      case "delete":
        // Restore the deleted annotation
        const restoredAnnotations = [...annotations, action.annotation];
        saveAnnotations(restoredAnnotations);
        break;
      case "update":
        // Restore previous state
        if (action.previousState) {
          const updatedAnnotations = annotations.map((ann) =>
            ann.id === action.annotation.id ? action.previousState! : ann
          );
          saveAnnotations(updatedAnnotations);
        }
        break;
    }
  }, [undoAnnotation, annotations, saveAnnotations]);

  const handleRedo = useCallback(() => {
    const action = redoAnnotation();
    if (!action) return;

    switch (action.type) {
      case "add":
        // Re-add the annotation
        const newAnnotations = [...annotations, action.annotation];
        saveAnnotations(newAnnotations);
        break;
      case "delete":
        // Re-delete the annotation
        const filteredAnnotations = annotations.filter(
          (ann) => ann.id !== action.annotation.id
        );
        saveAnnotations(filteredAnnotations);
        break;
      case "update":
        // Re-apply the update
        const updatedAnnotations = annotations.map((ann) =>
          ann.id === action.annotation.id ? action.annotation : ann
        );
        saveAnnotations(updatedAnnotations);
        break;
    }
  }, [redoAnnotation, annotations, saveAnnotations]);

  const handleClearAllAnnotations = useCallback(() => {
    if (annotations.length === 0) return;

    const confirmed = window.confirm(
      `Are you sure you want to delete all ${annotations.length} annotations? This action cannot be undone.`
    );
    if (confirmed) {
      // Add each annotation to history as deleted
      annotations.forEach((annotation) => {
        addToHistory({
          type: "delete",
          annotation,
        });
      });

      saveAnnotations([]);
      toast.success("All annotations cleared", {
        description: `${annotations.length} annotations have been removed.`,
      });
    }
  }, [annotations, addToHistory, saveAnnotations]);

  // Form handlers
  const handleFormSubmit = useCallback((data: FormData, validation: FormValidationResult) => {
    console.log("Form submitted:", data, validation);
    toast.success("Form submitted successfully!", {
      description: `${Object.keys(data).length} fields submitted.`,
    });
  }, []);

  const handleFieldAdd = useCallback((field: FormField) => {
    console.log("Field added:", field);
    setFormFields(prev => [...prev, field]);
    toast.success("Field added successfully!", {
      description: `${field.type} field "${field.name}" has been added.`,
    });
  }, []);

  return (
    <div ref={containerRef} className="h-screen flex flex-col bg-background mobile-optimized">
      {/* Sidebar */}
      <PDFSidebar
        isOpen={sidebarOpen}
        onClose={() => setSidebarOpen(false)}
        activeTab={activeTab}
        onTabChange={setActiveTab}
        pdfDocument={pdfDocument}
        numPages={numPages}
        currentPage={pageNumber}
        outline={outline}
        currentScale={scale}
        searchText={searchText}
        onSearchChange={setSearchText}
        onPageSelect={goToPage}
        bookmarks={bookmarks}
        onAddBookmark={addBookmark}
        onRemoveBookmark={removeBookmark}
        onUpdateBookmark={updateBookmark}
        annotations={annotations}
        selectedTool={selectedTool}
        onToolSelect={setSelectedTool}
        onAnnotationAdd={addAnnotation}
        onAnnotationUpdate={updateAnnotation}
        onAnnotationDelete={deleteAnnotation}
        formFields={formFields}
        formData={formData}
        onFormFieldsChange={setFormFields}
        onFormDataChange={saveFormData}
        onFormSubmit={handleFormSubmit}
        onFieldAdd={handleFieldAdd}
        performanceMode={activeTab === "performance"}
      />

      {/* Header */}
      <div className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 safe-area-top">
        <div className="flex items-center justify-between mobile-padding lg:p-4">
          <div className="flex items-center mobile-gap lg:gap-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="touch-target"
              aria-label="Close PDF viewer"
            >
              <X className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => openSidebar()}
              className="touch-target"
              aria-label="Open sidebar"
            >
              <Sidebar className="h-4 w-4" />
            </Button>
            <h1 className="font-semibold text-base sm:text-lg hidden xs:block">
              PDF Viewer
            </h1>
          </div>

          {selectedTool && (
            <div className="hidden lg:flex items-center gap-2 px-3 py-1 bg-primary/10 rounded-md">
              <PenTool className="h-3 w-3 text-primary" />
              <span className="text-xs font-medium text-primary capitalize">
                {selectedTool} Mode
              </span>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSelectedTool(null)}
                className="h-5 w-5 p-0"
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
          )}

          {/* Desktop Controls */}
          <div className="hidden lg:flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={goToPrevPage}
              disabled={pageNumber <= 1}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>

            <div className="flex items-center gap-2">
              <Input
                type="number"
                value={pageNumber}
                onChange={handlePageInputChange}
                className="w-16 text-center"
                min={1}
                max={numPages}
              />
              <span className="text-sm text-muted-foreground">
                of {numPages}
              </span>
            </div>

            <Button
              variant="ghost"
              size="sm"
              onClick={goToNextPage}
              disabled={pageNumber >= numPages}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>

            <Separator orientation="vertical" className="h-6" />

            <Button
              variant="ghost"
              size="sm"
              onClick={zoomOut}
              disabled={scale <= 0.5}
            >
              <ZoomOut className="h-4 w-4" />
            </Button>

            <span className="text-sm font-medium min-w-[4rem] text-center">
              {Math.round(scale * 100)}%
            </span>

            <Button
              variant="ghost"
              size="sm"
              onClick={zoomIn}
              disabled={scale >= 3.0}
            >
              <ZoomIn className="h-4 w-4" />
            </Button>

            <Button variant="ghost" size="sm" onClick={resetZoom}>
              Reset
            </Button>

            <Separator orientation="vertical" className="h-6" />

            <Button variant="ghost" size="sm" onClick={rotate}>
              <RotateCcw className="h-4 w-4" />
            </Button>

            <Button
              variant="ghost"
              size="sm"
              onClick={() => openSidebar("annotations")}
              className={selectedTool ? "text-primary bg-primary/10" : ""}
            >
              <PenTool className="h-4 w-4" />
            </Button>

            <Button
              variant="ghost"
              size="sm"
              onClick={() => openSidebar("forms")}
              className={
                formFields.length > 0 ? "text-blue-600 bg-blue-50" : ""
              }
            >
              <FileText className="h-4 w-4" />
            </Button>

            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                setIsFormDesignMode(!isFormDesignMode);
                openSidebar("form-designer");
              }}
              className={isFormDesignMode ? "text-purple-600 bg-purple-50" : ""}
            >
              <Edit className="h-4 w-4" />
            </Button>

            <Button
              variant="ghost"
              size="sm"
              onClick={() => openSidebar("workflows")}
              className="text-purple-600"
            >
              <GitBranch className="h-4 w-4" />
            </Button>

            <Button
              variant="ghost"
              size="sm"
              onClick={() => openSidebar("thumbnails")}
            >
              <Grid3X3 className="h-4 w-4" />
            </Button>

            <Button
              variant="ghost"
              size="sm"
              onClick={() => openSidebar("print")}
            >
              <Printer className="h-4 w-4" />
            </Button>

            <Button
              variant="ghost"
              size="sm"
              onClick={addBookmark}
              className={isPageBookmarked(pageNumber) ? "text-yellow-600" : ""}
            >
              {isPageBookmarked(pageNumber) ? (
                <Bookmark className="h-4 w-4 fill-current" />
              ) : (
                <BookmarkPlus className="h-4 w-4" />
              )}
            </Button>

            <Button variant="ghost" size="sm" onClick={toggleFullscreen}>
              {isFullscreen ? (
                <Minimize className="h-4 w-4" />
              ) : (
                <Maximize className="h-4 w-4" />
              )}
            </Button>

            <Button variant="ghost" size="sm" onClick={downloadPDF}>
              <Download className="h-4 w-4" />
            </Button>
          </div>

          {/* Mobile Menu */}
          <div className="lg:hidden">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setSidebarOpen(!sidebarOpen)}
              className="touch-target"
              aria-label="Open menu"
            >
              <Menu className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-auto bg-muted/20 relative mobile-scroll prevent-layout-shift">
        {isLoading && (
          <div className="absolute inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center">
            <div className="flex flex-col items-center gap-2">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              <p className="text-sm text-muted-foreground">Processing PDF...</p>
            </div>
          </div>
        )}
        <div className="flex justify-center mobile-padding lg:p-4">
          <Card className="shadow-lg w-full max-w-full">
            <Document
              file={file}
              onLoadSuccess={onDocumentLoadSuccess}
              onLoadError={onDocumentLoadError}
              loading={
                <div className="flex items-center justify-center p-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                </div>
              }
            >
              <PDFSimplePage
                pageNumber={pageNumber}
                scale={scale}
                rotation={rotation}
                className="shadow-sm"
                pdfDocument={pdfDocument}
                searchText={searchText}
                annotations={annotations}
                selectedTool={selectedTool}
                selectedColor={selectedColor}
                onAnnotationAdd={addAnnotation}
                formFields={formFields}
                formData={formData}
                onFormDataChange={saveFormData}
                isFormDesignMode={isFormDesignMode}
                enableAnnotations={true}
                enableForms={true}
                enableTextSelection={true}
                enableSearch={true}
                enableContextMenu={false}
              />
            </Document>
          </Card>
        </div>
      </div>

      {/* Mobile Navigation Footer */}
      <div className="lg:hidden border-t bg-background mobile-padding safe-area-bottom">
        {/* Primary Navigation Row */}
        <div className="flex items-center justify-between mobile-gap mb-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={goToPrevPage}
            disabled={pageNumber <= 1}
            className="touch-target-comfortable"
            aria-label="Previous page"
          >
            <ChevronLeft className="h-5 w-5" />
          </Button>

          <div className="flex items-center mobile-gap">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => openSidebar()}
              className="touch-target-comfortable"
              aria-label="Open sidebar"
            >
              <Sidebar className="h-5 w-5" />
            </Button>
            <span className="text-sm font-medium whitespace-nowrap px-2">
              {pageNumber} of {numPages}
            </span>
            {formFields.length > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => openSidebar("forms")}
                className="touch-target-comfortable"
                aria-label="Open forms"
              >
                <FileText className="h-5 w-5" />
              </Button>
            )}
          </div>

          <Button
            variant="ghost"
            size="sm"
            onClick={goToNextPage}
            disabled={pageNumber >= numPages}
            className="touch-target-comfortable"
            aria-label="Next page"
          >
            <ChevronRight className="h-5 w-5" />
          </Button>
        </div>

        {/* Secondary Controls Row */}
        <div className="flex items-center justify-center mobile-gap">
          <Button
            variant="ghost"
            size="sm"
            onClick={zoomOut}
            disabled={scale <= 0.5}
            className="touch-target"
            aria-label="Zoom out"
          >
            <ZoomOut className="h-4 w-4" />
          </Button>

          <span className="text-xs text-muted-foreground px-2">
            {Math.round(scale * 100)}%
          </span>

          <Button
            variant="ghost"
            size="sm"
            onClick={zoomIn}
            disabled={scale >= 3.0}
            className="touch-target"
            aria-label="Zoom in"
          >
            <ZoomIn className="h-4 w-4" />
          </Button>

          <Button
            variant="ghost"
            size="sm"
            onClick={addBookmark}
            className={`touch-target ${isPageBookmarked(pageNumber) ? "text-yellow-600" : ""}`}
            aria-label={isPageBookmarked(pageNumber) ? "Remove bookmark" : "Add bookmark"}
          >
            {isPageBookmarked(pageNumber) ? (
              <Bookmark className="h-4 w-4 fill-current" />
            ) : (
              <BookmarkPlus className="h-4 w-4" />
            )}
          </Button>
        </div>
      </div>

      {/* Floating Annotation Toolbar */}
      <PDFFloatingToolbarManager
        selectedTool={selectedTool}
        onToolSelect={setSelectedTool}
        selectedColor={selectedColor}
        onColorChange={setSelectedColor}
        annotationCount={annotations.length}
        onUndo={handleUndo}
        onRedo={handleRedo}
        onClearAll={handleClearAllAnnotations}
        canUndo={canUndo}
        canRedo={canRedo}
      />
    </div>
  );
}
